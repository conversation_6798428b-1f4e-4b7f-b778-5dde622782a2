*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\keil5\ARM\ARMCompiler_506_Windows_x86_b960\Bin'
Build target '24H_practice'
compiling pid_app.c...
..\User\App\pid_app.c(125): warning:  #177-D: variable "output_left"  was declared but never referenced
      int output_left = 0, output_right = 0;
..\User\App\pid_app.c(125): warning:  #177-D: variable "output_right"  was declared but never referenced
      int output_left = 0, output_right = 0;
..\User\App\pid_app.c: 2 warnings, 0 errors
linking...
Program Size: Code=16444 RO-data=456 RW-data=144 ZI-data=2464  
FromELF: creating hex file...
"24H_practice\24H_practice.axf" - 0 Error(s), 2 Warning(s).
Build Time Elapsed:  00:00:05
